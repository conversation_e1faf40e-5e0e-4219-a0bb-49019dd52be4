---
title: 渲染唯一标识（key）
sidebar_position: 2
tags: [FAQ]
---
渲染唯一标识（key）和 React 中组件的 key 属性的原理是一致的，都是为了在渲染场景或者组件切换的场景中唯一标识一个组件。

你可以在组件右侧配置面板的「高级」中看到此配置项，该配置项一般配合「是否渲染」和「循环」功能使用。
![image.png](https://img.alicdn.com/imgextra/i3/O1CN01wU7y30232jgLlfzRe_!!6000000007198-2-tps-560-696.png)

## 以下场景必需设置「渲染唯一标识」
#### 场景一：同类组件切换
以下场景中，当「爱好」选择「游戏」时显示「最喜欢的游戏」，选择「运动」时显示「最喜欢的运动」
![image.png](https://img.alicdn.com/imgextra/i2/O1CN016qHhJB1XWRfUJsml7_!!6000000002931-2-tps-1560-588.png)

配置方式如下：

1. 增加变量数据源：hobby
2. 「最喜欢的游戏」表单标识设置为 game，「是否渲染」绑定变量「state.hobby === '游戏'」

![image.png](https://img.alicdn.com/imgextra/i1/O1CN01oOemw41d0HY3qpwum_!!6000000003673-2-tps-2164-738.png)

3. 「最喜欢的运动」表单标识设置为 sport，「是否渲染」绑定变量「state.hobby === '运动'」
4. 「爱好」设置 onChange 动作

![image.png](https://img.alicdn.com/imgextra/i4/O1CN01oH4Giy1GTpwZwVSrO_!!6000000000624-2-tps-892-194.png)

5. 「提交」按钮绑定 onClick 动作

![image.png](https://img.alicdn.com/imgextra/i2/O1CN016kkf3O1uj1i9ev7uy_!!6000000006072-2-tps-750-134.png)

按以上配置（不配置渲染唯一标识），确实可以实现切换爱好时下方的文本框切换，但在提交数据时会发现，即使选择了「运动」，提交的时候 sport 字段是「最喜欢的游戏」的值。

原因：在进行文本框组件切换时，由于没有设置 key，底层会「复用」切换之前的组件

以上场景只需要给两个组件配置「渲染唯一标识」即可。
