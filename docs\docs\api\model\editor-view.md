---
title: Editor<PERSON><PERSON><PERSON>
sidebar_position: 12
---

> **[@experimental](./#experimental)**<br/>
> **@types** [IPublicModelEditorView](https://github.com/alibaba/lowcode-engine/blob/main/packages/types/src/shell/model/editor-view.ts)<br/>
> **@since** v1.1.7

窗口编辑视图

## 类型定义

```
import { IPublicModelPluginContext } from "./plugin-context";

export interface IPublicModelEditorView extends IPublicModelPluginContext {};

```

相关类型定义: [IPublicModelPluginContext](https://github.com/alibaba/lowcode-engine/blob/main/packages/types/src/shell/model/plugin-context.ts)
