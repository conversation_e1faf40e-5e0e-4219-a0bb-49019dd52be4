---
name: Bug report / 提交 bug
about: Create a report to help us improve / 提交一个好的 issue 帮助我们优化引擎，[引擎的 issue 说明](https://lowcode-engine.cn/site/community/issue)
title: ''
labels: ''
assignees: ''

---

## **Describe the bug (required)** / **详细描述 bug（必填）**

A clear and concise description of what the bug is. / 请提供清晰且精确的 bug 描述

---

## **To Reproduce (required)** / **如何复现 bug？（必填，非常重要）**

Steps to reproduce the behavior: / 详细复现步骤：

---

English version example：
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

中文版示例：
1. 打开 [demo](http://lowcode-engine.cn/demo)；
2. 点击标题；
3. 在右侧修改标题内容为「修改后的标题」；
4. 渲染画布标题组件没有更新显示为「修改后的标题」；

## **Expected behavior (required)** / **预期行为（必填，非常重要）**
A clear and concise description of what did you expect to happen. / 请清晰和精确的描述你预期的行为

---

## **Screenshots (optional)** / **bug 截图（可选）**
Sceenshots for further information. (If applicable.) / 一些有用的截图将会帮助我们更好的明确以及定位问题

---

## **Environments (please complete the following information) (required):** / **请提供如下信息（必填）**
 - AliLowCodeEngine version: [e.g. 1.0.0] / 低代码引擎版本
 - AliLowCodeEngineExt version: [e.g. 1.0.0] / 低代码引擎扩展包版本
 - Browser [e.g. chrome, safari] / 浏览器版本
 - materials / plugins / tools / 其他物料 / 插件 / 工具链版本

> (this information can be collected via [the manual plugin](https://img.alicdn.com/imgextra/i1/O1CN0115zonY1IsgbkZ2ir7_!!6000000000949-2-tps-3066-1650.png) / 版本信息可[通过低代码用户手册插件收集](https://img.alicdn.com/imgextra/i1/O1CN0115zonY1IsgbkZ2ir7_!!6000000000949-2-tps-3066-1650.png))

## **Additional context (optional)** / **更多额外信息（可选）**
Any other context of the problem here. / 可以追加更多的额外信息，帮助定位问题
