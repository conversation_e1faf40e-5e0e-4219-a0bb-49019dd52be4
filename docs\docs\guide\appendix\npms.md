---
title: NPM 包对应源码位置汇总
sidebar_position: 3
---
| 包名 | 仓库 | 路径 |
| --- | --- | --- |
| @alilc/lowcode-code-generator | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | modules/code-generator |
| @alilc/lowcode-material-parser | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | modules/material-parser |
| @alilc/lowcode-designer | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/designer |
| @alilc/lowcode-editor-core | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/editor-core |
| @alilc/lowcode-editor-skeleton | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/editor-skeleton |
| @alilc/lowcode-engine | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/engine |
| @alilc/lowcode-plugin-designer | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/plugin-designer |
| @alilc/lowcode-plugin-outline-pane | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/plugin-outline-pane |
| @alilc/lowcode-react-renderer | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/react-renderer |
| @alilc/lowcode-react-simulator-renderer | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/react-simulator-renderer |
| @alilc/lowcode-renderer-core | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/renderer-core |
| @alilc/lowcode-shell | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/shell |
| @alilc/lowcode-types  | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/types |
| @alilc/lowcode-utils | [https://github.com/alibaba/lowcode-engine](https://github.com/alibaba/lowcode-engine) | packages/utils |
| @alilc/lowcode-datasource-engine | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-engine |
| @alilc/lowcode-datasource-fetch-handler  | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-fetch-handler |
| @alilc/lowcode-datasource-jsonp-handler | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-jsonp-handler |
| @alilc/lowcode-datasource-mopen-handler  | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-mopen-handler |
| @alilc/lowcode-datasource-mtop-handler | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-mtop-handler |
| @alilc/lowcode-datasource-types | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-types |
| @alilc/lowcode-datasource-universal-mtop-handler | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-universal-mtop-handler |
| @alilc/lowcode-datasource-url-params-handler | [https://github.com/alibaba/lowcode-datasource](https://github.com/alibaba/lowcode-datasource) | packages/datasource-url-params-handler |
| @alilc/build-plugin-alt | [https://github.com/alibaba/lowcode-tools](https://github.com/alibaba/lowcode-tools) | packages/build-plugin-alt |
| @alilc/create-element | [https://github.com/alibaba/lowcode-tools](https://github.com/alibaba/lowcode-tools) | packages/create-element |
| @alilc/lowcode-plugin-inject | [https://github.com/alibaba/lowcode-tools](https://github.com/alibaba/lowcode-tools) | packages/lowcode-plugin-inject |
| @alilc/action-block | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/action-block |
| @alilc/lowcode-plugin-base-monaco-editor | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-base-monaco-editor |
| @alilc/lowcode-plugin-block | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-block |
| @alilc/lowcode-plugin-code-editor | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-code-editor |
| @alilc/lowcode-plugin-components-pane | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-components-pane |
| @alilc/lowcode-plugin-datasource-pane  | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-datasource-pane |
| @alilc/lowcode-plugin-manual | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-manual |
| @alilc/lowcode-plugin-schema | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-schema |
| @alilc/lowcode-plugin-undo-redo | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-undo-redo |
| @alilc/lowcode-plugin-zh-en | [https://github.com/alibaba/lowcode-plugins](https://github.com/alibaba/lowcode-plugins) | packages/plugin-zh-en |
| @alifd/fusion-ui | [https://github.com/alibaba/lowcode-materials](https://github.com/alibaba/lowcode-materials) | packages/fusion-ui |
| @alilc/lowcode-materials | [https://github.com/alibaba/lowcode-materials](https://github.com/alibaba/lowcode-materials) | packages/fusion-lowcode-materials |
| @alilc/antd-lowcode-materials | [https://github.com/alibaba/lowcode-materials](https://github.com/alibaba/lowcode-materials) | packages/antd-lowcode-materials |
| @alifd/layout（原 @alifd/pro-layout 升级后的版本） | [https://github.com/alibaba-fusion/layout](https://github.com/alibaba-fusion/layout) |  |
|  |  |  |
|  |  |  |
