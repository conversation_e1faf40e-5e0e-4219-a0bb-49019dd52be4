name: coverage

on:
  pull_request:
    paths:
      - 'packages/**'
      - '!packages/**.md'

jobs:
  cov-designer:
    runs-on: ubuntu-latest
    # skip fork's PR, otherwise it fails while making a comment
    if: ${{ github.event.pull_request.head.repo.full_name == 'alibaba/lowcode-engine' }}
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          working-directory: packages/designer
          test-script: npm test -- --jest-ci --jest-json --jest-coverage --jest-testLocationInResults --jest-outputFile=report.json
          package-manager: yarn
          annotations: none

  cov-renderer-core:
    runs-on: ubuntu-latest
    # skip fork's PR, otherwise it fails while making a comment
    if: ${{ github.event.pull_request.head.repo.full_name == 'alibaba/lowcode-engine' }}
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          working-directory: packages/renderer-core
          test-script: npm test -- --jest-ci --jest-json --jest-coverage --jest-testLocationInResults --jest-outputFile=report.json
          package-manager: yarn
          annotations: none

  cov-react-simulator-renderer:
    runs-on: ubuntu-latest
    # skip fork's PR, otherwise it fails while making a comment
    if: ${{ github.event.pull_request.head.repo.full_name == 'alibaba/lowcode-engine' }}
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          working-directory: packages/react-simulator-renderer
          test-script: npm test -- --jest-ci --jest-json --jest-coverage --jest-testLocationInResults --jest-outputFile=report.json
          package-manager: yarn
          annotations: none

  cov-utils:
    runs-on: ubuntu-latest
    # skip fork's PR, otherwise it fails while making a comment
    if: ${{ github.event.pull_request.head.repo.full_name == 'alibaba/lowcode-engine' }}
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          working-directory: packages/utils
          test-script: npm test -- --jest-ci --jest-json --jest-coverage --jest-testLocationInResults --jest-outputFile=report.json
          package-manager: yarn
          annotations: none