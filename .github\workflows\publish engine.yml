name: Publish Engine

on:
  workflow_dispatch:
    inputs:
      publishCommand:
        description: 'publish command'
        required: true

jobs:
  publish-engine:
    runs-on: ubuntu-latest
    if: >-
      contains(github.ref, 'refs/heads/release/') &&
      (github.actor == '1ncounter' || github.actor == 'liujuping')
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          registry-url: 'https://registry.npmjs.org'
      - run: npm install && npm run setup
      - run: |
          npm run build
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
      - run: npm run ${{ github.event.inputs.publishCommand }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Get version
        id: get_version
        run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
