{"version": "1.0.0", "componentsMap": [{"package": "@alilc/lowcode-materials", "version": "^1.0.0", "exportName": "Page", "destructuring": true, "componentName": "Page"}, {"package": "@alilc/lowcode-materials", "version": "^1.0.0", "exportName": "Typography", "destructuring": true, "subName": "Text", "componentName": "Text"}], "componentsTree": [{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {}, "fileName": "test", "dataSource": {"list": [{"id": "test", "type": "fetch", "options": {"uri": "https://xxx.com/api/xxx"}}]}, "css": "body {\n  font-size: 12px;\n}\n\n.botton {\n  width: 100px;\n  color: #ff00ff;\n}", "lifeCycles": {}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "state": {}, "children": [{"componentName": "Text", "props": {"text": "hello world"}}]}], "i18n": {}}