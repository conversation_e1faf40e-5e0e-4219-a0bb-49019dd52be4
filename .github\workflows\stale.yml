name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v4
        with:
          stale-issue-message: 'This issue is stale because it has been open 10 days with no activity. Remove stale label or comment or this will be closed in 2 days.'
          stale-pr-message: 'This PR is stale because it has been open 10 days with no activity. Remove stale label or comment or this will be closed in 2 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 10 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 10 days with no activity.'
          days-before-issue-stale: 10
          days-before-issue-close: 10
          days-before-pr-stale: 10
          days-before-pr-close: 10
          exempt-issue-labels: 'bug,enhancement,good first issue,help wanted,WIP,discussion,documentation,later,material'
          stale-issue-label: 'stale'
          stale-pr-label: 'stale'
          exempt-all-assignee: true