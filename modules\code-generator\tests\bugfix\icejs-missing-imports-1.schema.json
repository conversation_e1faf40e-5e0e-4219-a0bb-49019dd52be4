{"version": "1.0.0", "componentsMap": [{"destructuring": true, "exportName": "AliAutoSearchSelect", "main": "build/lowcode/index.js", "package": "@alife/mc-assets-1935", "version": "^0.1.45", "subName": "default", "componentName": "AliAutoSearchSelectDefault"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Form", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "subName": "<PERSON><PERSON>", "componentName": "Form.Item"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Col", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "Col"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Select", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "Select"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "<PERSON><PERSON>", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "<PERSON><PERSON>"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Row", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "Row"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Form", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "Form"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "P", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextP"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "Col", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextCol"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "Row", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextRow"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "RowColContainer", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextRowColContainer"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "BlockCell", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextBlockCell"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Typography", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "subName": "Text", "componentName": "Typography.Text"}, {"package": "@alilc/antd-lowcode-materials", "version": "0.17.0", "exportName": "Tag", "main": "dist/antd-lowcode.esm.js", "destructuring": true, "componentName": "Tag"}, {"devMode": "lowcode", "componentName": "Slot"}, {"destructuring": true, "exportName": "AliAutoSearchTable", "main": "build/lowcode/index.js", "package": "@alife/mc-assets-1935", "version": "^0.1.45", "subName": "default", "componentName": "AliAutoSearchTableDefault"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "Block", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextBlock"}, {"package": "@alife/pro-layout", "version": "^0.1.0", "exportName": "Page", "main": "lib/index.js", "destructuring": true, "componentName": "NextPage"}, {"devMode": "lowcode", "componentName": "Page"}], "componentsTree": [{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {"ref": "outterView", "style": {"height": "100%"}}, "fileName": "test", "dataSource": {"list": []}, "css": "body {\n  font-size: 12px;\n}\n\n.botton {\n  width: 100px;\n  color: #ff00ff;\n}", "lifeCycles": {"constructor": {"type": "JSFunction", "value": "function() {\n    this.__jp__init();\n    this.initUrl();\n    this.searchParams = {};\n    this.channelTimer = null;\n    this.currentChannel = null;\n  }"}, "componentDidMount": {"type": "JSFunction", "value": "function() {}"}, "componentWillUnmount": {"type": "JSFunction", "value": "function() {}"}}, "methods": {"__jp__init": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initRouter": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initDataSource": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initEnv": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initConfig": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initUtils": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__initUziModel": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__registerModel": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "__jp__finishedFn": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "initUrl": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "channelHandler": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "fetchProjects": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "setSearchParams": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "onPageChange": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "renderName": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "renderPlatform": {"type": "JSFunction", "value": "function(col) {\n    return col.platform_name;\n  }"}, "renderTime": {"type": "JSFunction", "value": "function(time) {\n    if (time) {\n      return this.$utils.moment(time).format('YYYY-MM-DD HH:mm');\n    }\n    return '暂无';\n  }"}, "onFinish": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "onSearchChannel": {"type": "JSFunction", "value": "function(value) {\n    this.fetchChannels(value);\n  }"}, "toNewAddPage": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "handlerModify": {"type": "JSFunction", "value": "function() { /* ... */ }"}, "infoTip": {"type": "JSFunction", "value": "function(msg) {\n    this.$utils.message.info(msg);\n  }"}, "renderCreator": {"type": "JSFunction", "value": "function() { /* ... */ }"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "state": {"total": 0, "projects": [], "channelOpts": [], "visible": false}, "children": [{"componentName": "NextPage", "id": "node_ocko19zplh1", "props": {"columns": 12, "headerDivider": true, "placeholderStyle": {"gridRowEnd": "span 1", "gridColumnEnd": "span 12"}, "placeholder": "页面主体内容：拖拽Block布局组件到这里", "header": "", "headerProps": {"background": "surface", "style": {"padding": ""}}, "footer": "", "minHeight": "100vh", "contentProps": {"noPadding": false, "background": "transparent"}, "presetNav": true, "presetAside": true}, "title": "页面", "condition": true, "hidden": false, "isLocked": false, "conditionGroup": "", "children": [{"componentName": "NextBlock", "id": "node_ockwu5hu4r3", "props": {"placeholderStyle": {"height": "100%"}, "noPadding": false, "noBorder": false, "title": "", "rowGap": 20, "colGap": 20, "background": "surface", "layoutmode": "O", "strict": true, "colSpan": 12, "rowSpan": 1, "mode": "transparent", "childTotalColumns": 12}, "title": "区域", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "loopArgs": ["", ""], "children": [{"componentName": "NextBlockCell", "id": "node_ockwu5hu4rj", "props": {"colSpan": 12, "rowSpan": 1, "mode": "procard", "isAutoContainer": true, "title": "", "childNum": 2, "childMode": "initial"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextRowColContainer", "id": "node_ockwu5hu4rk", "props": {"rowGap": 20, "colGap": 20, "strict": true}, "title": "行列容器", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextRow", "id": "node_ockwu5hu4rl", "props": {"strict": true}, "title": "行", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextCol", "id": "node_ockwu5hu4rm", "props": {"colSpan": 1, "strict": true}, "title": "列", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "loopArgs": ["", ""], "children": [{"componentName": "NextP", "id": "node_ockxie3rxc11g", "props": {"wrap": false, "type": "body2", "verAlign": "middle", "textSpacing": true, "align": "left", "full": true, "flex": true}, "title": "段落", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Form", "id": "node_ockxie3rxc11h", "props": {"labelCol": {"span": 6}, "wrapperCol": {"span": 18}, "onValuesChange": {"type": "JSExpression", "value": "function() {\n      const self = this;\n      try {\n        return (function onValuesChange(changedValues, allValues) {\n  console.log('onValuesChange', changedValues, allValues);\n}).apply(self, arguments);\n      } catch(e) {\n        console.log('call function which parsed by lowcode failed: ', e);\n        return e.message;\n      }\n    }"}, "onFinish": {"type": "JSExpression", "value": "function() {\n      const self = this;\n      try {\n        return (function onFinish(values) {\n  console.log('onFinish', values);\n}).apply(self, arguments);\n      } catch(e) {\n        console.log('call function which parsed by lowcode failed: ', e);\n        return e.message;\n      }\n    }"}, "onFinishFailed": {"type": "JSExpression", "value": "function() {\n      const self = this;\n      try {\n        return (function onFinishFailed({ values, errorFields, outOfDate }) {\n  console.log('onFinishFailed', values, errorFields, outOfDate);\n}).apply(self, arguments);\n      } catch(e) {\n        console.log('call function which parsed by lowcode failed: ', e);\n        return e.message;\n      }\n    }"}, "name": "basic", "layout": "horizontal"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Row", "id": "node_ockxie3rxc11v", "props": {"align": "middle", "gutter": [0, 0], "h-gutter": 0}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Col", "id": "node_ockxie3rxc11w", "props": {"span": 10}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Form.Item", "id": "node_ockxie3rxcq2", "props": {"label": "项目名称/渠道号", "labelAlign": "right", "colon": true, "name": "channel_id", "style": {"marginBottom": "0px"}}, "condition": true, "hidden": false, "title": "", "isLocked": false, "conditionGroup": "", "children": [{"componentName": "AliAutoSearchSelectDefault", "id": "node_ockxie4o9g2f", "props": {"type": "custom", "style": {"width": "100%"}, "init": true, "placeholder": "请搜索选择项目名称/渠道号", "config": {"url": {"type": "JSExpression", "value": "this.channelUrl"}, "searchKey": "search_param", "dataHandler": {"type": "JSFunction", "value": "function(){ return this.channelHandler.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "interval": 300}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "Col", "id": "node_ockxie3rxc11x", "props": {"span": 8}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Form.Item", "id": "node_ockxie3rxcpz", "props": {"label": "项目类型", "labelAlign": "right", "colon": true, "name": "project_type", "style": {"marginBottom": "0px"}}, "condition": true, "hidden": false, "title": "", "isLocked": false, "conditionGroup": "", "children": [{"componentName": "Select", "id": "node_ockxie3rxcq0", "props": {"style": {}, "options": [{"label": "平台化项目", "value": "platform_adapter"}, {"label": "纯适配项目", "value": "pure_adapter"}], "defaultActiveFirstOption": true, "size": "middle", "bordered": true, "filterOption": true, "optionFilterProp": "value", "allowClear": true, "placeholder": "请选择项目类型"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "loopArgs": ["", ""]}]}]}, {"componentName": "Col", "id": "node_ockxie3rxc11y", "props": {"span": 6, "style": {"textAlign": "right"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Form.Item", "id": "node_ockxie3rxcq5", "props": {"wrapperCol": {"offset": 6}, "style": {"marginBottom": "0px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "<PERSON><PERSON>", "id": "node_ockxie3rxcq6", "props": {"type": "primary", "children": "查询", "htmlType": "submit", "shape": "default", "size": "middle", "block": false}, "condition": true, "hidden": false, "title": "", "isLocked": false, "conditionGroup": ""}, {"componentName": "<PERSON><PERSON>", "id": "node_ockwu5hu4ri", "props": {"type": "link", "children": "新增项目", "htmlType": "button", "shape": "default", "size": "middle", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "toNewAddPage"}], "eventList": [{"name": "onClick", "templete": "onClick(${extParams}){\n// 点击按钮时的回调\nconsole.log('onClick');}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.toNewAddPage.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "style": {"marginLeft": "16px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}]}]}]}]}]}]}, {"componentName": "NextBlockCell", "id": "node_ockxie3rxc1", "props": {"colSpan": 12, "rowSpan": 1, "mode": "procard", "isAutoContainer": true, "title": "", "childNum": 2, "childMode": "initial"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextRowColContainer", "id": "node_ockxie3rxc2", "props": {"rowGap": 20, "colGap": 20, "strict": true}, "title": "行列容器", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextRow", "id": "node_ockxie3rxc3", "props": {"strict": true}, "title": "行", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "NextCol", "id": "node_ockxie3rxc4", "props": {"colSpan": 1, "strict": true}, "title": "列", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "AliAutoSearchTableDefault", "id": "node_ockxie3rxc5", "props": {"rowKey": "key", "dataSource": {"type": "JSExpression", "value": "this.state.projects"}, "columns": [{"title": "项目名称", "dataIndex": "project_name", "key": "name"}, {"title": "代码名称", "dataIndex": "coding_name", "key": "address"}, {"title": "项目类型", "dataIndex": "project_type", "render": {"type": "JSFunction", "value": "function(){ return this.renderName.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, {"title": "渠道号", "dataIndex": "channelId", "render": {"type": "JSSlot", "params": ["text", "record", "index"], "value": [{"componentName": "Typography.Text", "id": "node_ockxijxx0a3", "props": {"children": "text"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "Tag", "id": "node_ockxijxwe42", "props": {"color": "magenta", "children": {"type": "JSExpression", "value": "this.text", "mock": "tag"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}}, {"title": "更新时间", "dataIndex": "update_time", "render": {"type": "JSFunction", "value": "function(){ return this.renderTime.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "width": 160}, {"title": "操作人", "dataIndex": "creator", "width": 90, "render": {"type": "JSFunction", "value": "function(){ return this.renderCreator.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}], "actions": [{"text": "修改", "needConfirm": false, "handler": {"type": "JSFunction", "value": "function(){ return this.handlerModify.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}], "isPagination": true, "pagination": {"defaultPageIndex": 1, "defaultPageSize": 10, "total": {"type": "JSExpression", "value": "this.state.total"}, "onPageChange": {"type": "JSFunction", "value": "function(){ return this.onPageChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "size": "small", "loading": {"type": "JSExpression", "value": "this.state.LOADING_PROJECTS"}}, "condition": true, "hidden": false, "title": "", "isLocked": false, "conditionGroup": "", "loopArgs": ["", ""]}]}]}]}]}]}]}]}], "i18n": {}}