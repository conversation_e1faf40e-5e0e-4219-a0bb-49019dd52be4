---
title: Procode 物料如何调用数据源方法
sidebar_position: 12
tags: [FAQ]
---
## 解决方案
给物料插入如下配置，可以默认给物料提供 reloadDataSource 的参数。
```json
{
  title: {
    label: {
      type: 'i18n',
      'en-US': 'reloadDataSource',
      'zh-CN': 'reloadDataSource',
    },
  },
  name: 'reloadDataSource',
  setter: 'StringSetter',
  initialValue: () => (
    {
      "type": "JSFunction",
      "value": "function(){ return this.reloadDataSource; }"
    }
  ),
},
```

在物料组件中，即可掉用如下代码来获取到相关方法。
```json
const reloadDataSource = props.getReloadDataSource()
```

## FAQ
### 希望该配置在配置面板中不展示
在配置中加上
```json
condition: () => false,
```

完整示例
```json
{
  title: {
    label: {
      type: 'i18n',
      'en-US': 'reloadDataSource',
      'zh-CN': 'reloadDataSource',
    },
  },
  name: 'reloadDataSource',
  setter: 'StringSetter',
  condition: () => false,
  initialValue: () => (
    {
      "type": "JSFunction",
      "value": "function(){ return this.reloadDataSource; }"
    }
  ),
},
```

### 配置没有生效
查看组件中的 schema，对应的配置是否已经正确设置。
![image.png](https://img.alicdn.com/imgextra/i2/O1CN015AGT4l1MwaVGGwgua_!!6000000001499-2-tps-1046-1154.png)

没有正确设置上可能的原因是
1.snippets 中没有默认值
需要按照如下的代码中，加上默认的参数配置
```json
const snippets: Snippet[] = [
  {
    title: 'Field',
    screenshot: '',
    schema: {
      componentName: 'ProField',
      props: {
        type: 'textarea',
        value: '我是测试',
        getReloadDataSource: {
          "type": "JSFunction",
          "value": "function(){ return this.reloadDataSource; }"
        }
      },
    },
  },
];
```

### 如何全局生效
通过 [registerMetadataTransducer API](/site/docs/api/material#registermetadatatransducer) 来修改元数据信息，注意如果有 snippets 相关配置也需要修改相关的配置。
