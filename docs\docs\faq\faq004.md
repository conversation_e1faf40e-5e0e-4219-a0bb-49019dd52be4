---
title: 最小渲染单元配置
sidebar_position: 4
tags: [FAQ]
---
## 背景
在低代码引擎画布中，每一个节点的更新是**增量更新**机制。也就是通过 API 监听到组件的参数配置变化的时候，只更新该节点。

一些场景下，父组件需要在子组件的属性变化，获取新的子组件的属性值，也就是从父组件开始渲染。
例如：父组件需要强制修改 children props 值。示例代码如下：
```
React.Children.forEach(children, (child: React.ReactElement) => {
  // 子元素的参数只有 behavior，且 behavior 为 'READONLY';
	const newChild = React.cloneElement(child, {
  	behavior: 'READONLY'
  });
})
```

**对于这种场景，需要配置其为“最小渲染单元”****。**即：
> **最小渲染单元下的组件渲染和更新都从单元的根节点开始渲染和更新。如果嵌套了多层最小渲染单元，渲染会从最外层的最小渲染单元开始渲染。**


### 组件能力配置 component
| **字段** | **用途** | **类型** |
| --- | --- | --- |
| isContainer(A) | 是否容器组件 | Boolean |
| **isMinimalRenderUnit** | **默认值：false**
**是否是最小渲染单元** | **Boolean** |
| ... |  |  |

#### 配置示例
##### 标准配置文件
configure.component.isMinimalRenderUnit
```json
{
    "componentName": "Table",
    "title": "表格",
    "category": "数据展示",
    "props": [],
    "configure": {
        "supports": {
        },
        "props": [],
        "component": {
            // 添加如下配置
            "isMinimalRenderUnit": true
        },
        "combined": []
    },
    "snippets": [],
    "npm": {}
}
```
## 更新机制说明

1. 没有任何组件被标识为**最小渲染单元**，则是每个组件都伴随自身属性变更而重新渲染；
2. 将根组件标识为**最小渲染单元**，则是整个页面重新渲染；
3. 组件树的分支节点被标识为**最小渲染单元**，则分支节点之下（包括自身）节点属性变更，触发分支节点整体的重新渲染；（若有多个**最小渲染单元**在同一条路径上，从最外层的**最小渲染单元**开始渲染）
