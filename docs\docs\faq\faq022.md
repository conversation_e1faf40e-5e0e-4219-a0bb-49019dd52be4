---
title: 节点无法拖拽到 Page 下
sidebar_position: 22
tags: [FAQ]
---
查看 Page 节点的 childWhitelist 配置，如果 Page 配置了 childWhitelist，且当前节点不在白名单下，是无法拖拽的。
```typescript
AliLowCodeEngine.material.getComponentMeta('Page').getMetadata().configure.component.nestingRule.childWhitelist
```

比如在 [demo](https://lowcode-engine.cn/demo/demo-general/index.html) 中 Page 组件的 childWhitelist 为：['NextPage', 'ProDialog', 'Dialog', 'Drawer']，则只有这些组件可以拖拽到 Page 的 children 下，其他组件均不可以。

说明：1.0.15 之前 Page 组件的 childWhitelist 限制是失效的，在 1.0.16 版本进行了 bug 修复。

### 解决办法
**方法 1：直接修改 Page 组件的 childWhitelist，比如删除**。

**方法 2：通过 **[**material.registerMetadataTransducer**](/site/docs/api/material#registermetadatatransducer)** 修改 Page 组件的 childWhitelist（适用于 Page 组件是其他人维护的）**
