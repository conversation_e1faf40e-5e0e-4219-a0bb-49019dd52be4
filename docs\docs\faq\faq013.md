---
title: Modal 类组件 hidden 属性被强制设置 true
sidebar_position: 13
tags: [FAQ]
---
## 注意
弹窗的正确弹出方式请参考：[如何通过按钮展示/隐藏弹窗](/site/docs/demoUsage/makeStuff/dialog)
## 问题原因
由于 hidden 属性，导致 Modal 组件在预览的时候不渲染，也就无法获取到实例。
## 处理方式
### 【推荐】升级到 Engine Verison 1.0.11 以上
### 新增 save propsReducer

通过新增 Save 态的 propsReducer，将 hidden props 去掉。可以参考下面的代码：

```typescript
import { project } from '@alilc/lowcode-engine';
import { IPublicEnumTransformStage } from '@alilc/lowcode-types';

export const deleteHiddenTransducer = (ctx: any) => {
  return {
    name: 'deleteHiddenTransducer',
    async init() {
      project.addPropsTransducer((props: any): any => {
        delete props.hidden;
        return props;
      }, IPublicEnumTransformStage.Save);
    },
  };
}

deleteHiddenTransducer.pluginName = 'deleteHiddenTransducer';

```

### 导出 schema 使用 Save 态
```typescript
import { TransformStage } from '@alilc/lowcode-types';

const schema = project.exportSchema(TransformStage.Save)
```
