/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
const getDocsFromDir = require('../scripts/getDocsFromDir');

module.exports = {
  // 手动配置的导航
  // guide: [
  //   'guide/quickStart/intro',
  //   'guide/quickStart/start',
  //   {
  //     type: 'category',
  //     label: 'FAQ',
  //     collapsed: false,
  //     items: getDocsFromDir('guide/quickStart/faq'),
  //   },
  // ],
  /**
   * 根据当前目录自动生成导航配置
   */
  guide: [
    [
      {
        type: 'category',
        label: '入门',
        collapsed: false,
        items: getDocsFromDir('guide/quickStart'),
      },
      {
        type: 'category',
        label: '创建编辑器',
        collapsed: false,
        items: getDocsFromDir('guide/create'),
      },
      {
        type: 'category',
        label: '扩展编辑器',
        collapsed: false,
        items: getDocsFromDir('guide/expand/editor', [{ dir: 'guide/expand/editor/parts', label: 'Parts·造物' }]),
      },
      {
        type: 'category',
        label: '扩展运行时',
        collapsed: false,
        items: getDocsFromDir('guide/expand/runtime'),
      },
      {
        type: 'category',
        label: '设计原理',
        collapsed: false,
        items: getDocsFromDir('guide/design'),
      },
      {
        type: 'category',
        label: '附录',
        collapsed: false,
        items: [
          {
            type: 'link',
            label: '更新日志',
            href: 'https://github.com/alibaba/lowcode-engine/releases',
          },
          ...getDocsFromDir('guide/appendix'),
          {
            type: 'category',
            label: '预置设置器详情',
            items: getDocsFromDir('guide/appendix/setterDetails'),
          },
        ],
      },
      {
        type: 'link',
        label: '技术白皮书',
        href: 'https://developer.aliyun.com/ebook/7507',
      },
    ],
  ],
  api: [
    {
      type: 'autogenerated',
      dirName: 'api',
    },
  ],
  specs: [
    {
      type: 'autogenerated',
      dirName: 'specs',
    },
  ],
  faq: [
    {
      type: 'autogenerated',
      dirName: 'faq',
    },
  ],
  participate: [
    {
      type: 'autogenerated',
      dirName: 'participate',
    },
  ],
  demoUsage: [
    {
      type: 'autogenerated',
      dirName: 'demoUsage',
    },
  ],
};
