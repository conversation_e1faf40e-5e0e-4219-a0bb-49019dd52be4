{"version": "1.0.0", "componentsMap": [{"package": "react-greetings", "version": "1.0.0", "componentName": "Greetings", "exportName": "Greetings", "destructuring": true}], "componentsTree": [{"componentName": "Page", "id": "node_ocl137q7oc1", "fileName": "test", "props": {"style": {}}, "lifeCycles": {}, "dataSource": {"list": []}, "state": {"name": "lowcode world"}, "methods": {}, "children": [{"componentName": "Greetings", "id": "node_ocl137q7oc4", "props": {"content": {"type": "i18n", "key": "greetings.hello", "params": {"name": {"type": "JSExpression", "value": "this.state.name"}}}}}]}], "i18n": {"zh-CN": {"greetings.hello": "${name}, 你好！"}, "en-US": {"greetings.hello": "Hello, ${name}!"}}}