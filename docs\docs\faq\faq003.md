---
title: 点击事件如何添加参数
sidebar_position: 3
tags: [FAQ]
---
背景：

- [Antd Table 下 button 点击事件怎么拿到行数据？](https://github.com/alibaba/lowcode-engine/issues/341)
## 方式 1
![image.png](https://img.alicdn.com/imgextra/i4/O1CN01i58EGG1bxFJBdlS6x_!!6000000003531-2-tps-3342-1126.png)

参考 fusion protable，将操作列直接耦合 button 组件，因为 col.render 函数能拿到 行数据 record，那么 pro-table 组件封装的时候，就可以在渲染操作列按钮的时候，将 col.render 参数透传给 button 组件

## 方式 2
slot + 扩展参数

![image.png](https://img.alicdn.com/imgextra/i2/O1CN01pQk2RC1WBXyxjNDif_!!6000000002750-2-tps-3284-1148.png)

将扩展参数写成：
```json
{
  record: this.record,
  index: this.index
}
```

那事件处理函数的第二个参数即可得到：
```json
onClick_new_new(...args){
  console.log(args)
}
```
