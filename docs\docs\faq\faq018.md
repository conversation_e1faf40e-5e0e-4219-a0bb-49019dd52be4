---
title: 是否可以生成 Vue 页面代码？
sidebar_position: 18
tags: [FAQ]
---
低代码引擎在架构上是和具体语言无关的，通过一定的扩展和插件是可以生成 Vue 页面代码的。
如果只是用现有的基于 React 的 fusion 物料来搭建，只是在最终出码的时候生成 Vue 页面代码，那您需要准备一套和 fusion 兼容的 vue 物料，并定制个出码方案，将[下面的一些出码插件](https://github.com/alibaba/lowcode-engine/blob/main/modules/code-generator/src/solutions/icejs.ts)替换成生成 Vue 框架的即可：
![image.png](https://img.alicdn.com/imgextra/i3/O1CN01VxkwCL1l85DiDC2BO_!!6000000004773-2-tps-974-1368.png)
详细定制方案可以参考下[《自定义出码》](/site/docs/guide/expand/runtime/codeGeneration#5自定义出码)。
如果您希望在搭建的时候也使用 Vue 的物料，则还需要扩展定制入料、画布和渲染器等模块，详细方案请参考下[《扩展低代码编辑器》](/site/docs/guide/expand/editor/summary)
