---
title: Slot组件渲染报错问题
sidebar_position: 23
tags: [FAQ]
---

## 问题描述
在低代码引擎的页面渲染过程中，可能会遇到一个关于Slot组件的报错，提示“Slot找不到”。实际上，在渲染态时不应使用Slot组件。

## 问题原因
低代码引擎渲染分为两个状态：设计态和渲染态。
- **设计态**：为了帮助插槽进行可视化设计，引入了Slot组件。
- **渲染态**：在此状态下，不需要使用Slot组件。

这个问题通常是因为在渲染态错误地使用了设计态的schema。

## 解决方案
1. **区分设计态和渲染态**：通过`project.exportSchema(TransformStage.Save)`的参数来区分。
   - `TransformStage.Save`代表渲染态的schema，其中不包含Slot组件。
   - 【默认值】`TransformStage.Render`代表设计态的schema，其中包含Slot组件。
2. **使用正确的API和参数**：确保在渲染态使用正确的schema，避免引用设计态的Slot组件。
3. **处理脏数据问题**：如果问题是由脏数据导致，清除数据并重新拖拽组件以恢复正常。

## 注意事项
- 确保在代码和配置中正确区分设计态和渲染态。
- 如果遇到持续的问题，检查是否有脏数据或配置错误，并进行相应的清理和调整。

## 相关链接
- Issue链接：[Issue #1798](https://github.com/alibaba/lowcode-engine/issues/1798)

---
