---
title: workspace 模式常见问题
sidebar_position: 23
tags: [FAQ]
---

#### 如何判断是否开启了IDE模式？

- **通过官方API判断**：您可以通过访问 [workspace.isActive](/site/docs/api/workspace#isactive) 来判断当前是否处于IDE模式。这是阿里低代码引擎提供的一个官方API，专门用于确认是否处于集成开发环境。



#### 如何使用插件的ctx来做判断在哪个模式下？

- **插件是否为应用级别**：可以通过 **ctx.isPluginRegisteredInWorkspace** 方法来判断一个插件是否是应用级别的插件。这有助于理解插件在阿里低代码引擎中的作用域和潜在的使用场景。
- **插件的注册级别**：您可以使用 **ctx.registerLevel** 属性来判断插件处于哪个级别。插件级别的值包括：
  - **Default**：默认级别。非 IDE 模式下的值
  - **Workspace**：应用级别。
  - **Resource**：资源级别。
  - **EditorView**：编辑视图级别。 这些级别代表了插件可能的作用域和使用场景，有助于在开发和管理低代码应用时对插件进行更精确的控制和配置。



#### 如何在IDE模式下设置资源列表？

- **设置资源列表API**：在IDE模式下，可以通过访问 [workspace.setResourceList](/site/docs/api/workspace#setresourcelist) 来设置或更新IDE中的资源列表。这确保您在编辑器窗口中打开的资源是最新且可访问的。



#### 如何打开视图窗口？

- **使用推荐的方法**：使用 `openEditorWindow(resource: Resource, sleep?: boolean): Promise<void>;` 来打开视图窗口。这里的 **resource** 参数指的是您要打开的特定资源，可通过 [workspace.resourceList](/site/docs/api/workspace#resourcelist) 获取。
- **不推荐使用的过时方法**：有一个过时的方法 `openEditorWindow(resourceName: string, id: string, extra: Object, viewName?: string, sleep?: boolean): Promise<void>;` 也用于打开视图窗口。虽然仍然可用，但官方不推荐使用此方法，并计划在后续版本中废弃，因为它在维护和可扩展性方面存在限制。



#### 如何在全局插件中获取视图的上下文？

- 在阿里低代码引擎的全局插件中获取视图的上下文，可以通过使用 **ProvideViewPluginContext** 函数实现。这个函数来自 **@alilc/lowcode-utils** 库，它使得您的 React 组件能够接收 **pluginContext** 作为 props，进而访问和操作当前视图的状态和属性。

**步骤**

**引入依赖**：首先，确保您的插件文件中已经引入了 **ProvideViewPluginContext** 以及其他必要的依赖。

```
import { ProvideViewPluginContext } from '@alilc/lowcode-utils';
```

**定义 React 组件**：创建一个 React 组件，它将使用来自 **ProvideViewPluginContext** 的 **pluginContext**。

```typescript
const MyComponent = (props) => {
  const { pluginContext } = props;
  // 组件逻辑
  return <div>/* 组件内容 */</div>;
};
```

**定义全局插件**：定义一个函数，这个函数会在插件被注册时调用。这个函数通常接受一个上下文对象 **ctx**，它提供了对引擎功能的访问。

```javascript
const globalPlugin = (ctx) => {
  const { skeleton } = ctx;

  skeleton.add({
    type: 'PanelDock',
    name: 'datapool',
    content: ProvideViewPluginContext((props) => {
      // 组件内容
      return (
        <MyComponent {...props} />
      )
    }),
    // 其他配置
    contentProps: {
      // 需要提供 pluginContext 作为参数
      pluginContext: ctx,
    }
  });
};
```

通过这些步骤，您的全局插件中的 React 组件就能够获取并使用视图的上下文了。这为您在插件中实现更复杂的功能和交互提供了基础。



**注意事项**

- **组件重渲染**：正常情况下，**pluginsContext** 是视图的上下文。当视图切换时，组件会重新渲染。如果需要在组件中处理视图切换导致的重新渲染，可以利用 React 的 **key** 属性。

**示例代码**

```typescript
ProvideViewPluginContext(props => {
  return (
    <DataPoolPane
      {...props}
      key={props.pluginContext?.editorWindow?.id}
    );
});
```

通过这种方式，当视图切换时，组件会根据视图的不同进行重新渲染，确保组件状态与当前视图的上下文保持一致。这对于在低代码平台上开发复杂插件和交互功能是非常有用的。



#### 如何判断插件是否在 Workspace 模式下注册？

**使用** **ctx.isPluginRegisteredInWorkspace()** **方法**:

通过 **ctx.isPluginRegisteredInWorkspace()** 方法，可以判断一个插件是否在 Workspace 级别注册。以下是一个示例代码片段：

```javascript
if (ctx.isPluginRegisteredInWorkspace('pluginName')) {
    console.log('插件已在 Workspace 模式下注册。');
} else {
    console.log('插件未在 Workspace 模式下注册。');
}
```

注意：此方法目前在 beta 版本中，可能会有 TypeScript 提示显示已移除。

**检查** **ctx.registerLevel** **的值**:

可以通过比较 **ctx.registerLevel** 的值来判断插件的注册级别。示例代码如下：

```javascript
if (ctx.registerLevel !== IPublicEnumPluginRegisterLevel.Workspace) {
    console.log('插件未在 Workspace 模式下注册。');
} else {
    console.log('插件已在 Workspace 模式下注册。');
}
```
