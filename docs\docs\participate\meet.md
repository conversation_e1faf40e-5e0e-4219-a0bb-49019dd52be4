---
title: 开源社区例会
sidebar_position: 0
---

## **简介**

低代码引擎开源社区致力于共同推动低代码技术的发展和创新。本社区汇集了低代码技术领域的开发者、技术专家和行业观察者，通过定期的例会来交流思想、分享经验、讨论新技术，并探索低代码技术的未来发展方向。

## 参与要求

为了确保例会的质量和效果，我们建议以下人员参加：

- **已参与低代码引擎贡献的成员**：那些对低代码引擎有实际贡献的社区成员。
- **参考贡献指南**：可查阅[贡献指南](https://lowcode-engine.cn/site/docs/participate/)获取更多信息。
- **提供过优秀建议的成员**：那些在过去为低代码引擎提供过有价值建议的成员。

## **时间周期**

- **周期性**：月例会

### **特别说明**

- 例会周期可根据成员反馈进行调整。如果讨论的议题较多，可增加例会频率；若议题较少，单次例会可能取消。若多次取消，可能会暂停例会。

## **例会流程**

### **准备阶段**

- **定期确定议题**：会前一周确定下一次会议的议题。
- **分发会议通知**：提前发送会议时间、议程和参与方式。

### **会议阶段**

- **开场和介绍**：简短开场和自我介绍，特别是新成员加入时。
- **议题讨论**：按照议程进行议题讨论，每个议题分配一定时间，并留足够时间供讨论和提问。
- **记录要点和决定**：记录讨论要点、决策和任何行动事项。

### **后续阶段**

- **分享会议纪要**：会后将会议纪要和行动计划分发给所有成员。
- **执行和跟进**：根据会议中的讨论和决策执行相关任务，并在下次会议中进行跟进汇报。

## **开源例会议题**

开源例会议题包括但不限于：

- **共建低代码行业发展**：探讨通过开源社区合作加速低代码行业发展。
- **改进建议和反馈收集**：讨论社区成员对低代码引擎的使用体验和改进建议。
- **前端技术与低代码的结合**：针对前端开发者，讨论将前端技术与低代码引擎结合的方式。
- **低代码业务场景和经验分享**：邀请社区成员分享低代码引擎的实际应用经验。
- **低代码技术原理介绍**：深入理解低代码引擎的技术原理和实现方式。
- **低代码引擎的最新进展**：分享低代码引擎的最新进展，包括新版本发布和新功能实现等。
- **低代码技术的未来展望**：讨论低代码技术的未来发展方向。
- **最新低代码平台功能和趋势分析**：分享和讨论当前低代码平台的新功能、趋势和发展方向。