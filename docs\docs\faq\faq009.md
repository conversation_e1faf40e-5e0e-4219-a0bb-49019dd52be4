---
title: 物料出现 Component Not Found 相关报错
sidebar_position: 9
tags: [FAQ]
---
## 预览态，antd 资产包按顺序加载，但是没有按顺序执行
资产包按顺序加载，但是没有按顺序执行，导致部分 js 执行的时候，依赖的资源没有准备好，报错了。
传给  @alilc/lowcode-react-renderer 的 components 值为空。

**解决方案**
LowCodeEngine 升级到 1.0.8
> 相关 PR：[https://github.com/alibaba/lowcode-engine/pull/383](https://github.com/alibaba/lowcode-engine/pull/383)


## 编辑态，snippets 和注入组件不对应
1.在控制台中输入
```json
AliLowCodeEngine.material.componentsMap
```
查看物料配置是否正常。

![image.png](https://img.alicdn.com/imgextra/i4/O1CN01bAsPoT1QOTSp7Fmz5_!!6000000001966-2-tps-1640-816.png)

如果正常继续。
LowCodeEngine 需要升级到 1.0.10
```json
AliLowCodeEngine.project.simulator.renderer.components
```
看看对应的物料是否存在，如果不存在，排查物料问题。

如果不正常，查看资产包配置，其中资产包中的 `components` 和 `material.componentsMap` 生成有关系。

例如，物料配置信息在 @alilc/lowcode-materials 包里面，即需要在 components 中加上下面的代码

```javascript
"components": [{
  "exportName": "AlilcLowcodeMaterialsMeta",
  "npm": {
    "package": "@alilc/lowcode-materials"
  },
  "url": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.js",
  "urls": {
    "default": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.js",
    "design": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.design.js"
  }
}]
```

`material.componentsMap` 不存在相关的组件信息，原因有两个：
- 没有添加对应的物料到 components 字段中
- components 配置不正确，需要查看 url 是否正常加载，查看 exportName 是否配置正确，即 `window.${exportName}` 是否存在。

2.选中组件，在控制台中输入
```json
AliLowCodeEngine.project.currentDocument.selection.getNodes()[0].exportSchema('render')
```
查看 componentName 是否匹配。

3.调用 rerender 方法
```json
AliLowCodeEngine.project.simulator.rerender()
```
看一下问题是否恢复。

## 排查物料问题
找到对应组件的资产包，比如下图的资产包。
```json
{
  "package": "@yingzhi8/lowcode-public-package",
  "version": "0.1.2",
  "library": "BizComps",
  "urls": [
    "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/render/default/view.js",
    "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/render/default/view.css"
  ],
  "editUrls": [
    "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/view.js",
    "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/view.css"
  ],
  "advancedUrls": {
    "default": [
      "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/render/default/view.js",
      "https://unpkg.com/@yingzhi8/lowcode-public-package@0.1.2/build/lowcode/render/default/view.css"
    ]
  },
  "advancedEditUrls": {}
}
```

### 查看 urls 是否加载
通过查看控制台，看加载的 urls
### library 配置是否正确
library 是可以在画布上访问到全局变量，确定  library 是否正确，在控制台输入：
```json
AliLowCodeEngine.project.simulator.contentWindow.${library}
```
