{
	"version": "1.0.0",
	"componentsMap": [
		{
			"componentName": "Button",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Button"
		},
		{
			"componentName": "Button.Group",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Button",
			"subName": "Group"
		},
		{
			"componentName": "Input",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Input"
		},
		{
			"componentName": "Form",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Form"
		},
		{
			"componentName": "Form.Item",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Form",
			"subName": "Item"
		},
		{
			"componentName": "NumberPicker",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "NumberPicker"
		},
		{
			"componentName": "Select",
			"package": "@alifd/next",
			"version": "1.19.18",
			"destructuring": true,
			"exportName": "Select"
		}
	],
	"componentsTree": [
		{
			"componentName": "Page",
			"id": "node$1",
			"meta": {
				"title": "测试",
				"router": "/"
			},
			"props": {
				"ref": "outterView",
				"autoLoading": true
			},
			"fileName": "test",
			"state": {
				"text": "outter"
			},
			"lifeCycles": {
				"componentDidMount": {
					"type": "JSFunction",
					"value": "function() { console.log('componentDidMount'); }"
				}
			},
			dataSource: {
        list: [
          {
            id: 'urlParams',
            type: 'urlParams',
          },
          // 示例数据源：https://shs.xxx.com/mock/1458/demo/user
          {
            id: 'user',
            type: 'fetch',
            options: {
              method: 'GET',
              uri: 'https://shs.xxx.com/mock/1458/demo/user',
              isSync: true,
            },
            dataHandler: {
              type: 'JSExpression',
              value: 'function (response) {\nif (!response.data.success){\n    throw new Error(response.data.message);\n  }\n  return response.data.data;\n}',
            },
          },
          // 示例数据源：https://shs.xxx.com/mock/1458/demo/orders
          {
            id: 'orders',
            type: 'fetch',
            options: {
              method: 'GET',
              uri: "https://shs.xxx.com/mock/1458/demo/orders",
              isSync: true,
            },
            dataHandler: {
              type: 'JSExpression',
              value: 'function (response) {\nif (!response.data.success){\n    throw new Error(response.data.message);\n  }\n  return response.data.data.result;\n}',
            },
          },
        ],
        dataHandler: {
          type: 'JSExpression',
          value: 'function (dataMap) {\n  console.info("All datasources loaded:", dataMap);\n}',
        },
      },
			"children": [
				{
					"componentName": "Form",
					"id": "node$2",
					"props": {
						"labelCol": {
							"type": "JSExpression",
							"value": "this.state.colNum"
						},
						"style": {},
						"ref": "testForm"
					},
					"children": [
						{
							"componentName": "Form.Item",
							"id": "node$3",
							"props": {
								"label": "姓名：",
								"name": "name",
								"initValue": "李雷"
							},
							"children": [
								{
									"componentName": "Input",
									"id": "node$4",
									"props": {
										"placeholder": "请输入",
										"size": "medium",
										"style": {
											"width": 320
										}
									}
								}
							]
						},
						{
							"componentName": "Form.Item",
							"id": "node$5",
							"props": {
								"label": "年龄：",
								"name": "age",
								"initValue": "22"
							},
							"children": [
								{
									"componentName": "NumberPicker",
									"id": "node$6",
									"props": {
										"size": "medium",
										"type": "normal"
									}
								}
							]
						},
						{
							"componentName": "Form.Item",
							"id": "node$7",
							"props": {
								"label": "职业：",
								"name": "profession"
							},
							"children": [
								{
									"componentName": "Select",
									"id": "node$8",
									"props": {
										"dataSource": [
											{
												"label": "教师",
												"value": "t"
											},
											{
												"label": "医生",
												"value": "d"
											},
											{
												"label": "歌手",
												"value": "s"
											}
										]
									}
								}
							]
						},
						{
							"componentName": "Div",
							"id": "node$9",
							"props": {
								"style": {
									"textAlign": "center"
								}
							},
							"children": [
								{
									"componentName": "Button.Group",
									"id": "node$a",
									"props": {},
									"children": [
										{
											"componentName": "Button",
											"id": "node$b",
											"condition": {
												"type": "JSExpression",
												"value": "this.index >= 1"
											},
											"loop": ["a", "b", "c"],
											"props": {
												"type": "primary",
												"style": {
													"margin": "0 5px 0 5px"
												},
											},
											"children": [
												{
													"type": "JSExpression",
													"value": "this.item"
												}
											]
										}
									]
								}
							]
						}
					]
				}
			]
		}
	],
	"constants": {
		"ENV": "prod",
		"DOMAIN": "xxx.xxx.com"
	},
	"css": "body {font-size: 12px;} .table { width: 100px;}",
	"config": {
		"sdkVersion": "1.0.3",
		"historyMode": "hash",
		"targetRootID": "J_Container",
		"layout": {
			"componentName": "BasicLayout",
			"props": {
				"logo": "...",
				"name": "测试网站"
			}
		},
		"theme": {
			"package": "@alife/theme-fusion",
			"version": "^0.1.0",
			"primary": "#ff9966"
		}
	},
	"meta": {
		"name": "demo应用",
		"git_group": "appGroup",
		"project_name": "app_demo",
		"description": "这是一个测试应用",
		"spma": "spa23d",
		"creator": "月飞"
	}
}
