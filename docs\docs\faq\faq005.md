---
title: 如何通过 this.utils 使用第三方工具扩展
sidebar_position: 5
tags: [FAQ]
---
## 设计器
### 通过引擎 API 配置
[API-init](/site/docs/api/init)

### 通过资产包

![image.png](https://img.alicdn.com/imgextra/i4/O1CN01WWJVSA1WutBvCzXnl_!!6000000002849-2-tps-806-788.png)

就可以在引擎代码中访问到 moment
![image.png](https://img.alicdn.com/imgextra/i1/O1CN01EEJ0Kp1nZgJm68nSG_!!6000000005104-2-tps-1248-252.png)

PS：需要在 packages 中有相关的资源配置，例如：
![image.png](https://img.alicdn.com/imgextra/i4/O1CN01bdiHVv206uRYvvAAr_!!6000000006801-2-tps-1322-420.png)

否则在画布中可能会访问不到对应的资源。

## 预览态
[参考资料](/site/docs/guide/expand/runtime/renderer#apphelper)
