---
title: VERSION_PLACEHOLDER is not defined
sidebar_position: 14
tags: [FAQ]
---
# 问题原因
由于 lowcode-engine 目前只提供 cdn 的使用方式。如果是自己创建的项目，遇到这个报错了，主要是因为将  npm 包打包进去了。

# 解决方案

## engine-demo 项目
在项目的 externals 配置里加[一行配置](https://github.com/alibaba/lowcode-demo/blob/f8afad0df3190565caccc0a1dfd750dbf84c680f/build.json#L16)

## 其他项目
[相关文档](/site/docs/guide/create/useEditor#引入-umd-包资源)
### webpack
[https://webpack.docschina.org/configuration/externals/](https://webpack.docschina.org/configuration/externals/)

### 使用文档
待补充
