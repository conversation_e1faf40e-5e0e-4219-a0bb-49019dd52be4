---
title: 如何获取物料当前处于编辑态还是渲染态
sidebar_position: 11
tags: [FAQ]
---
## 简单场景
可以利用 props.__designMode

![image.png](https://img.alicdn.com/imgextra/i3/O1CN01btr66024FOEldBOr2_!!6000000007361-2-tps-1616-440.png)

设计态中，__designMode 值为 "design"

渲染态中，__designMode 没有设置值

## 复杂场景
在资产包里定义 editUrls

![image.png](https://img.alicdn.com/imgextra/i1/O1CN01odal6P27Rhjn8NoJ6_!!6000000007794-2-tps-1590-538.png)

### editUrls
在 lowcode/xx/ 下新建一个 view.tsx

![image.png](https://img.alicdn.com/imgextra/i3/O1CN01q0Bbn91Lrig7d0alA_!!6000000001353-2-tps-598-154.png)

再执行
```json
npm run lowcode:build
```

之后，build/lowcode 目录下既有 view.js，可作为 editUrls 配置在资产包中。

![image.png](https://img.alicdn.com/imgextra/i1/O1CN01dvIZ441alxwIlwexS_!!6000000003371-2-tps-1082-986.png)
