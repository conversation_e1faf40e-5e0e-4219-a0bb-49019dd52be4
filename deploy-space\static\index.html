<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge,chrome=1" />
    <meta name="viewport" content="width=device-width" />
    <title>LowCodeEngine Editor DEMO</title>
    <link rel="shortcut icon" href="./favicon.png" />
    <script src="https://g.alicdn.com/code/lib/react/16.9.0/umd/react.development.js"></script>
    <script src="https://g.alicdn.com/code/lib/react-dom/16.9.0/umd/react-dom.development.js"></script>
    <script src="https://g.alicdn.com/code/lib/prop-types/15.7.2/prop-types.js"></script>
    <script> React.PropTypes = PropTypes; </script>
    <script src="https://g.alicdn.com/platform/c/??react15-polyfill/0.0.1/dist/index.js,lodash/4.6.1/lodash.min.js,immutable/3.7.6/dist/immutable.min.js,natty-storage/2.0.2/dist/natty-storage.min.js,natty-fetch/2.6.0/dist/natty-fetch.pc.min.js,tinymce/4.2.5/tinymce-full.js"></script>
    <script src="https://g.alicdn.com/mylib/moment/2.24.0/min/moment.min.js"></script>
    <link rel="stylesheet" href="https://alifd.alicdn.com/npm/@alifd/next/1.11.6/next.min.css" />
    <script src="https://unpkg.alibaba-inc.com/@alifd/next@1.18.17/dist/next.min.js"></script>
    <!-- lowcode engine globals -->
    <link rel="stylesheet" href="./editor-preset-vision.css" />
    <!-- lowcode engine app -->
    <link rel="stylesheet" href="./lowcode-editor.css" />
    <script>
      window.pageConfig = {
        env: 'release',
        locale: 'zh-CN',
        pageType: 'single',
        deviceType: 'web',
        appName: '基础包管理后台',
        appType: '',
        templateType: '',
        pageId: 'FORM-3KYJN7RV-DIOD8LLK1WGQ89S7NHA92-QJVH497K-V',
        slug: 'test',
        appMode: 'back',
        isAppAdmin: 'y',
        isSuperAdmin: 'n',
        isBetaDeveloper: 'n',
        formType: 'display',
        title: { 'en-US': 'Test', type: 'i18n', 'zh-CN': '测试' },
        urlPrefix: 'https://go.alibaba-inc.com',
        APIUrlPrefix: 'https://mocks.alibaba-inc.com/mock/lowCodeEngine',
        devVersion: '0.1.0', // 这个是子应用的变更 id
        subAppType: '0.1.0',
        appKey: '',
        RE_VERSION: '7.1.1',
        appSource: '',
        isDomainDefault: 'n',
        useReleaseBundle: 'n',
        isDomainPkg: 'n',
        medusaAppName: '',
        domainCode: 'kS6SyH',
        aecp: {
          mdcDomain: '',
          projectId: '',
          appCode: '',
        },
        designerConfigs: {},
        navConfig:
          '{"appName":{"en-US":"基础包管理后台","key":"","type":"i18n","zh-CN":"基础包管理后台"},"bgColor":"white","data":[{"children":[],"hidden":false,"icon":"","inner":true,"navUuid":"FORM-3KYJN7RV-DIOD8LLK1WGQ89S7NHA92-QJVH497K-V","relateUuid":"FORM-3KYJN7RV-DIOD8LLK1WGQ89S7NHA92-QJVH497K-V","slug":"test","targetNew":false,"title":{"en-US":"测试","type":"i18n","zh-CN":"测试"}}],"isFixed":"y","isFold":"y","isFoldHorizontal":"n","languageChangeUrl":{"en-US":"/common/account/changeAccountLanguage.json","type":"i18n","zh-CN":"/common/account/changeAccountLanguage.json"},"layout":"auto","navStyle":"orange","navTheme":"light","openSubMode":false,"showAppTitle":true,"showCrumb":true,"showIcon":false,"showLanguageChange":true,"showNav":true,"showSearch":"n","singletons":{"FORM-3KYJN7RV-DIOD8LLK1WGQ89S7NHA92-QJVH497K-V":{"isFixed":"n","isFold":"n","isFoldHorizontal":"n","showAppTitle":false,"showCrumb":false,"showLanguageChange":false,"showNav":false,"showSearch":"n","singleton":false},"test":{"$ref":"$.singletons.FORM\\-3KYJN7RV\\-DIOD8LLK1WGQ89S7NHA92\\-QJVH497K\\-V"}},"type":"top_fold"}',
        historyType: 'HASH',
        isSinglePage: 'n',
        rhino: 'n',
        isMiniApp: '',
        taskId: '',
        appSchema: 'V5',
        openSubMode: 'n',
      };
      window.g_config = {};
    </script>
  </head>

  <body>
    <div id="lce-container"></div>
    <!-- lowcode engine globals -->
    <script src="./editor-preset-vision.js"></script>
    <script src="https://dev.g.alicdn.com/vision/visualengine-utils/5.0.0/engine-utils.js"></script>
    <link rel="stylesheet" type="text/css" href="//g.alicdn.com/??platform/common/s/1.1/global/global.css,uxcore/uxcore-kuma/2.2.1/orange.min.css">
    <!-- lowcode engine app -->
    <script src="./lowcode-editor.js"></script>
  </body>
</html>
