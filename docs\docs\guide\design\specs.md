---
title: 协议栈简介
sidebar_position: 1
---
## 什么是低代码协议
低代码引擎体系基于三份协议来构建，分别是 [《低代码引擎搭建协议规范》](/site/docs/specs/lowcode-spec)、[《低代码引擎物料协议规范》](/site/docs/specs/material-spec)和[《低代码引擎资产包协议规范》](/site/docs/specs/assets-spec), 它们保障了低代码领域的标准化，成为了生态建设和流通的基石。

![image.png](https://img.alicdn.com/imgextra/i3/O1CN01axsOyW1s01YgXnT8z_!!6000000005703-2-tps-1888-1000.png)

## 为什么需要协议

首先，我们做一个不恰当的类比，我们将低代码引擎和 JavaScript 语言做一下类别。还记得之前，大家都被浏览器兼容性支配的恐惧，特别是 IE 和其他浏览器，对上层 API 实现的不一致，导致一份代码需要运行在两端需要做适配。当浏览器 / JavaScript 相关的标准出现之后，各个浏览器进行了 API 的统一，使得我们终于可以从这部分工作中解放出来（PS：Babel 对于语言特性的转换是另一个方面的问题）。

而在《低代码引擎搭建协议规范》出现之前，低代码领域也有类似的问题。

### 概念不通

在交流的过程中，一些对于搭建产品的术语的不一致，导致了一些沟通成本，不管是在文章分享、技术分享、交流会上，都会有这个问题。

### 物料孤岛

由于低代码产品实现的方式不同，物料的消费方式也各不相同。这里分为两种物料，低代码物料和 ProCode 物料。

对于低代码物料来说，A 平台创建的物料无法使用到 B 平台上，如果想在 B 平台实现同样的物料，需要按照 B 平台的标准搭建一份物料。

对于 ProCode 物料来说，需要在低代码平台进行消费，是需要进行转换的，包括搭建配置项的生成、物料搭建视图等，可能还需要特殊的描述文件进行描述。由于这一层没有统一，同一份 ProCode 物料每接入一个低代码，可能需要的描述文件格式不同，转换的代码不同，使用的工具也不同。

### 生态隔离

不同低代码平台的生态体系也不相同，有的低代码平台的物料生态不错，有的低代码平台的搭建体验生态不错。但是这些利好的生态，都是无法互通的，甚至就算知道了代码也无法复用，因为底层是不一致的。对于阿里巴巴集团来说，每一个平台都创建一份自己的生态，这并不是利好的。

### 低水平重复建设

大家可能觉得，以上问题对于自己造轮子来说，其实也是有利的，因为自己得到了技术上的成长。

但是对于低代码的平台方，实际上更多的工作，在物料的转化、物料的生成、搭建体验的小优化、部分其他平台生态的实现。这些的技术深度其实并不高，属于低水平重复建设部分。

### 价值不高

如果每个业务都要从 0 开始做，做自己的平台，会花费大量的时间来构建底层基础设施，对业务本身而言并不是一件好事；而且前端领域的底层基础设施都大同小异，不同团队重复构建造成了极大的资源浪费。

这样的建设，会导致从 0 到 1 都需要花费大量的时间，往往在内部人力不足、投入有限时，产品很容易在未发展壮大的时候就面临了死亡相关的决策。

设想一下，如果可以开发一份全集团低代码平台都可以使用的物料，是否更有成就感呢？如果可以基于已有生态进行低代码平台的快速落地，而不是花费 1-2 年搭建一个可用的低代码平台，再验证市场。在快速的验证之后，再进行更深入的打磨，这其中的思考和技术含量是否更优于之前的模式呢？

以 2019 年的阿里巴巴的情况举例，不同平台的低代码物料但不限于：

1. vc-deep — vc 协议 + Deep 组件库 (阿里巴巴企业智能团队基于 Fusion Next 定制)；
2. Iceluna 协议 + Fusion Next；
3. AIMake 物料；
4. vc-fusion-basic + 业务改造 — vc 协议 + Fusion Next(各业务 Fork 定制)；
5. vision 魔改 + vc 协议扩展 + fusion 业务组件；
6. vc 协议 + antd；

可以看到，各个搭建平台都需要维护一套自己的基础组件库，这是非常不合理的，对基础组件库的维护会分散开发同学完成业务目标的精力。

建立统一的低代码领域标准化，是百利而无一害的。于是，在阿里巴巴集团 2020 年进行了讨论，建立了搭建治理&物料流通战役，此战役便产出了上文中的协议规范，成为了低代码引擎和其生态的基础。

## 协议的作用

基于统一的协议，我们完成业务组件、区块、模板等各类物料的标准统一，各类中后台研发系统生产的物料可借助物料中心进行跨系统流通，通过丰富物料生态的共享提升各平台研发系统的效率。同时完成低代码引擎的标准统一以及低代码搭建中台能力的输出，帮助业务方快速孵化本业务域中后台研发系统。

### 打破物料孤岛

#### 物料中心

这里以阿里集团的前端物料中间建设为例，在《低代码引擎物料协议规范》落地之后，建立了阿里巴巴各个中后台研发平台沟通、对话的基础，物料流通的先决条件已经成熟，这个时候我们还需要一个统一的物料源，用于管理物料的上传、存储、检索、分发，一个典型的中心化架构，类似 npm 的管理，这便是我们物料中心。

Fusion Market 是物料中心的前身，它提供了业务组件的存储、文档展示和全局透出的功能，由于 fusion 体系在集团内的广泛使用，Fusion Market 沉淀了不少的业务组件，但是这个项目却一直不温不火，只看到业务组件数量的增加，却未看到物料流通起来。其中一个原因是，没有阿里巴巴前端委员会的背书，规范很难统一，规范如果不统一，物料就很难流通；

在规范成立之后，物料中心也将有了建设的基础，最终于 2019 年建立了物料中心，提供了物料流通的平台能力。

#### 低代码基础物料

就像 AntD、Element 之于源码研发模式，在低代码研发模式下各个搭建平台也需要一套统一的、开箱即用的低代码基础组件库。基于低代码描述协议完成了两份低代码基础物料的建设，即“Fusion 低代码基础组件库”和“AntD 低代码基础组件库”。

#### 源码组件低代码化

将源码组件一键转化为低代码物料，符合低代码物料规范，可以在低代码平台进行流通。
### 低代码物料中心

当低代码物料积累到一定的量级之后，所有的搭建平台的业务物料越来越多。这些物料通过低代码物料中心进行统一的管理和消费。
### 设置器生态的基础

Snippet(组件默认搭建 schema ) 由《低代码引擎搭建协议规范》定义，低代码引擎会按照规范对组件进行渲染，Configure 由《低代码引擎物料协议规范》定义，它描述了组件的 props 以及每个 prop 对应的设置器 (Prop 配置面板)，低代码引擎提供了 20+ 个内置设置器，但如果我们组件的 props 超出了引擎内置设置器的范围，就需要我们自己来开发对应设置器。
设置器最终也慢慢形成了自己的生态，这使得开发物料更加容易，可以使用已有的生态中的设置器，进行物料配置描述。
### 低代码引擎实现标准

低代码引擎是以上生态的消费端，它是实现了标准协议的低代码引擎。这是不可或缺的部分，低代码引擎这里就相当于一个标准浏览器，一方面给其他的低代码平台提供了一个 Demo，其他平台可以参考低代码引擎进行实现，满足官方协议，便也可以消费相关的物料生态和其他生态。
