name: Lint & Test (Pkgs)

on:
  push:
    paths:
      - 'packages/**'
      - '!packages/**.md'
  pull_request:
    paths:
      - 'packages/**'
      - '!packages/**.md'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: lint
        run: npm run lint

  test-designer:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/designer && npm test

  test-editor-skeleton:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/editor-skeleton && npm test

  test-renderer-core:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/renderer-core && npm test

  test-react-simulator-renderer:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/react-simulator-renderer && npm test

  test-utils:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/utils && npm test

  test-editor-core:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/editor-core && npm test

  test-plugin-command:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: install
        run: npm i && npm run setup:skip-build

      - name: test
        run: cd packages/plugin-command && npm test