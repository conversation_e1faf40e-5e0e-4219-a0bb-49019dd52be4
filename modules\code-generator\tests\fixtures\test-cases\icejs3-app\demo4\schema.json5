{"version": "1.0.0", "componentsMap": [{"package": "@alife/mc-assets-1935", "version": "0.1.9", "exportName": "AliSearchTable", "main": "build/lowcode/index.js", "subName": "default", "destructuring": true, "componentName": "AliSearchTable"}, {"package": "@alife/container", "version": "^1.0.0", "exportName": "P", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextP"}, {"package": "@alife/container", "version": "^1.0.0", "exportName": "Block", "main": "lib/index.js", "destructuring": true, "subName": "Cell", "componentName": "NextBlockCell"}, {"package": "@alife/container", "version": "^1.0.0", "exportName": "Block", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextBlock"}, {"package": "@alife/container", "version": "^1.0.0", "exportName": "Text", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextText"}, {"package": "@alife/container", "version": "^1.0.0", "exportName": "Page", "main": "lib/index.js", "destructuring": true, "subName": "", "componentName": "NextPage"}], "componentsTree": [{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {"ref": "outterView", "style": {"height": "100%"}}, "fileName": "test", "dataSource": {"list": [{"type": "fetch", "isInit": true, "options": {"params": {}, "method": "GET", "isCors": true, "timeout": 5000, "headers": {}, "uri": "https://mocks.xxx.com/mock/jjpin/user/list"}, "id": "users"}]}, "css": "body {\n  font-size: 12px;\n}\n\n.botton {\n  width: 100px;\n  color: #ff00ff\n}", "lifeCycles": {"componentDidMount": {"type": "JSFunction", "value": "function() {\n    console.log('did mount');\n  }"}, "componentWillUnmount": {"type": "JSFunction", "value": "function() {\n    console.log('will umount');\n  }"}, "componentDidUpdate": {"type": "JSFunction", "value": "function(prevProps, prevState, snapshot) {\n    console.log(this.state);\n  }"}}, "methods": {"testFunc": {"type": "JSFunction", "value": "function() {\n    console.log('test func');\n  }"}, "onClick": {"type": "JSFunction", "value": "function() {\n    this.setState({\n      isShowDialog: true\n    })\n  }"}, "closeDialog": {"type": "JSFunction", "value": "function() {\n    this.setState({\n      isShowDialog: false\n    })\n  }"}, "onSearch": {"type": "JSFunction", "value": "function(values) {\n    console.log('search form:', values)\n    console.log(this.dataSourceMap);\n    this.dataSourceMap['users'].load(values)\n  }"}, "onClear": {"type": "JSFunction", "value": "function() {\n    console.log('form reset')\n    this.setState({\n      isShowDialog: true\n    })\n  }"}, "onPageChange": {"type": "JSFunction", "value": "function(page, pageSize) {\n    console.log(`page: ${page}, pageSize: ${pageSize}`)\n  }"}}, "state": {"text": "outter", "isShowDialog": false}, "children": [{"componentName": "NextPage", "id": "node_ockkgjwi8z1", "props": {"columns": 12, "placeholderStyle": {"gridRowEnd": "span 1", "gridColumnEnd": "span 12"}, "placeholder": "页面主体内容：拖拽Block布局组件到这里", "header": {"type": "JSSlot", "value": [{"componentName": "NextP", "id": "node_ockkgjwi8zn", "props": {"wrap": true, "type": "body2", "verAlign": "middle", "textSpacing": true, "align": "left", "flex": true}, "children": [{"componentName": "NextText", "id": "node_ockkgjwi8zo", "props": {"type": "h5", "children": "员工列表"}}]}], "title": "header"}, "headerTest": {"type": "JSSlot", "value": [], "title": "header"}, "headerProps": {"background": "surface"}, "footer": {"type": "JSSlot", "title": "footer"}, "minHeight": "100vh"}, "children": [{"componentName": "NextBlock", "id": "node_ockkgjwi8z2", "props": {"prefix": "next-", "placeholderStyle": {"height": "100%"}, "noPadding": false, "noBorder": false, "background": "surface", "colSpan": 12, "rowSpan": 1, "childTotalColumns": "1fr"}, "title": "分区", "children": [{"componentName": "NextBlockCell", "id": "node_ockkgjwi8z3", "props": {"title": "", "primaryKey": "732", "prefix": "next-", "placeholderStyle": {"height": "100%"}, "colSpan": 1, "rowSpan": 1}, "children": [{"componentName": "NextP", "id": "node_ockkgjwi8zu", "props": {"wrap": true, "type": "body2", "textSpacing": true, "verAlign": "center", "align": "flex-start", "flex": true}, "children": [{"componentName": "AliSearchTable", "id": "node_ockkgjwi8zv", "props": {"dataSource": {"type": "JSExpression", "value": "this.state.users.data"}, "rowKey": "workid", "columns": [{"title": "花名", "dataIndex": "cname"}, {"title": "user_id", "dataIndex": "workid"}, {"title": "部门", "dataIndex": "dep"}], "searchItems": [{"label": "姓名", "name": "cname"}, {"label": "部门", "name": "dep"}], "onSearch": {"type": "JSFunction", "value": "function(){ return this.onSearch.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "onClear": {"type": "JSFunction", "value": "function(){ return this.onClear.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "pagination": {"defaultPageSize": "", "onPageChange": {"type": "JSFunction", "value": "function(){ return this.onPageChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "showSizeChanger": true}}}]}]}]}]}, {"componentName": "NextPage", "id": "node_ockm4jxd6313", "props": {"columns": 12, "headerDivider": true, "placeholderStyle": {"gridRowEnd": "span 1", "gridColumnEnd": "span 12"}, "placeholder": "页面主体内容：拖拽Block布局组件到这里", "header": {"type": "JSSlot", "title": "header"}, "headerProps": {"background": "surface"}, "footer": {"type": "JSSlot", "title": "footer"}, "minHeight": "100vh"}, "title": "页面", "children": [{"componentName": "NextBlock", "id": "node_ockm4jxd6314", "props": {"prefix": "next-", "placeholderStyle": {"height": "100%"}, "noPadding": false, "noBorder": false, "background": "surface", "colSpan": 12, "rowSpan": 1, "childTotalColumns": 1}, "title": "区块", "children": [{"componentName": "NextBlockCell", "id": "node_ockm4jxd6315", "props": {"title": "", "primaryKey": "472", "prefix": "next-", "placeholderStyle": {"height": "100%"}, "colSpan": 1, "rowSpan": 1}}]}]}]}], "i18n": {}}