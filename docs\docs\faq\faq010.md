---
title: 插件面板如何调整位置
sidebar_position: 10
tags: [FAQ]
---
使用 index 配置来定义位置，内置的默认都是 0
```json
AliLowCodeEngine.skeleton.add({
  area: 'leftArea',
  type: 'PanelDock',
  name: 'xxx',
  content: () => 'xxx',
  index: -1, // 使用 index 来定义位置，内置的默认都是 0
  props: { icon: () => 'x' /* ReactElement 也可以 */ },
});
```
这里设置 index 为负数，可以将其调整到第一的位置。
![image.png](https://img.alicdn.com/imgextra/i4/O1CN01PTCH3r20fiXrrbcXe_!!6000000006877-2-tps-1614-1158.png)
