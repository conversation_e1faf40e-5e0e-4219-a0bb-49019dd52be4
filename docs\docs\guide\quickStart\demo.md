---
title: 试用低代码引擎 Demo
sidebar_position: 2
---
## 访问地址

低代码引擎的 Demo 可以通过如下永久链接访问到：

[设计器 demo](https://lowcode-engine.cn/demo/demo-general/index.html)

> 注意我们会经常更新 demo，所以您可以通过上述链接得到最新版地址。


## 低代码引擎 Demo 功能概览

我们可以从 Demo 的项目中看到页面中有很多的区块：

![image.png](https://img.alicdn.com/imgextra/i2/O1CN01vlxdTD28c4JZcebbf_!!6000000007952-2-tps-3840-2160.png)

它主要包含这些功能点：

![image.png](https://img.alicdn.com/imgextra/i2/O1CN01QITHRY1sQaWzlvJv9_!!6000000005761-2-tps-3840-2160.png)

### 顶部：操作区

- 右侧：撤回和重做、保存到本地、重置页面、预览、异步加载资源
### 左侧：面板与操作区
- 大纲面板：可以调整页面内的组件树结构
- 物料面板：可以查找组件，并在此拖动组件到编辑器画布中
- 源码面板：可以编辑页面级别的 JavaScript 代码和 CSS 配置
- 提交 Issue：可以给引擎开发提 bug
- Schema 编辑：可以编辑页面的底层数据
- 中英文切换：可以切换编辑器的语言

### 中部：可视化页面编辑画布区域
- 点击组件在右侧面板中能够显示出对应组件的属性配置选项
- 拖拽修改组件的排列顺序
- 将组件拖拽到容器类型的组件中
- 复制组件：点击组件右上角的复制按钮
- 删除组件：点击组件右上角的 X 或者直接使用 `Delete` 键

### 右侧：组件级别配置
- 选中的组件：从页面开始一直到当前选中的组件位置，点击对应的名称可以切换到对应的组件上
- 选中组件的配置：当前组件的大类目选项，根据组件类型不同，包含如下子类目：
  - 属性：组件的基础属性值设置
  - 样式：组件的样式配置
  - 事件：绑定组件对外暴露的事件
  - 高级：循环、条件渲染与 key 设置

## 深入使用低代码引擎 Demo

我们在低代码引擎 Demo 中直接内置了产品使用文档，对常见场景中的使用进行了向导，它的入口如下：

![image.png](https://img.alicdn.com/imgextra/i3/O1CN01YU2LYS29YEbuLTtLL_!!6000000008079-2-tps-3070-1650.png)

如果暂时没有看到对应的产品使用文档，可以通过此永久链接直接访问：[https://lowcode-engine.cn/site/docs/demoUsage/intro](https://lowcode-engine.cn/site/docs/demoUsage/intro)
